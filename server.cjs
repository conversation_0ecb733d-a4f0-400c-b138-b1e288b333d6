/**
 * 简单的本地开发服务器
 * 用于测试飞行棋游戏
 */

const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

// 服务器配置
const PORT = 3000;
const HOST = 'localhost';

// MIME类型映射
const mimeTypes = {
  '.html': 'text/html',
  '.js': 'text/javascript',
  '.css': 'text/css',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpeg',
  '.gif': 'image/gif',
  '.svg': 'image/svg+xml',
  '.wav': 'audio/wav',
  '.mp3': 'audio/mpeg',
  '.mp4': 'video/mp4',
  '.woff': 'application/font-woff',
  '.ttf': 'application/font-ttf',
  '.eot': 'application/vnd.ms-fontobject',
  '.otf': 'application/font-otf',
  '.wasm': 'application/wasm'
};

// 获取文件的MIME类型
function getMimeType(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  return mimeTypes[ext] || 'application/octet-stream';
}

// 创建HTTP服务器
const server = http.createServer((req, res) => {
  // 解析URL
  const parsedUrl = url.parse(req.url);
  let pathname = parsedUrl.pathname;
  
  // 如果是根路径，重定向到index.html
  if (pathname === '/') {
    pathname = '/index.html';
  }
  
  // 构建文件路径
  const filePath = path.join(__dirname, pathname);
  
  // 检查文件是否存在
  fs.access(filePath, fs.constants.F_OK, (err) => {
    if (err) {
      // 文件不存在，返回404
      res.writeHead(404, { 'Content-Type': 'text/html' });
      res.end(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>404 - 文件未找到</title>
          <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
            h1 { color: #e74c3c; }
            a { color: #3498db; text-decoration: none; }
            a:hover { text-decoration: underline; }
          </style>
        </head>
        <body>
          <h1>404 - 文件未找到</h1>
          <p>请求的文件 <code>${pathname}</code> 不存在</p>
          <p><a href="/">返回首页</a></p>
        </body>
        </html>
      `);
      return;
    }
    
    // 读取文件
    fs.readFile(filePath, (err, data) => {
      if (err) {
        // 读取文件失败，返回500
        res.writeHead(500, { 'Content-Type': 'text/html' });
        res.end(`
          <!DOCTYPE html>
          <html>
          <head>
            <title>500 - 服务器错误</title>
            <style>
              body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
              h1 { color: #e74c3c; }
              a { color: #3498db; text-decoration: none; }
              a:hover { text-decoration: underline; }
            </style>
          </head>
          <body>
            <h1>500 - 服务器错误</h1>
            <p>读取文件时发生错误</p>
            <p><a href="/">返回首页</a></p>
          </body>
          </html>
        `);
        return;
      }
      
      // 获取MIME类型
      const mimeType = getMimeType(filePath);
      
      // 设置响应头
      const headers = {
        'Content-Type': mimeType,
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Cache-Control': 'no-cache'
      };
      
      // 如果是JavaScript文件，设置正确的MIME类型
      if (pathname.endsWith('.js')) {
        headers['Content-Type'] = 'application/javascript';
      }
      
      // 返回文件内容
      res.writeHead(200, headers);
      res.end(data);
      
      // 记录访问日志
      const timestamp = new Date().toISOString();
      console.log(`[${timestamp}] ${req.method} ${pathname} - 200`);
    });
  });
});

// 错误处理
server.on('error', (err) => {
  if (err.code === 'EADDRINUSE') {
    console.error(`\n❌ 端口 ${PORT} 已被占用，请尝试以下解决方案：`);
    console.error(`   1. 关闭占用端口的程序`);
    console.error(`   2. 使用其他端口：node server.js --port 3001`);
    console.error(`   3. 查看占用进程：netstat -ano | findstr :${PORT}\n`);
  } else {
    console.error('服务器错误:', err);
  }
  process.exit(1);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n\n🛑 正在关闭服务器...');
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});

// 启动服务器
server.listen(PORT, HOST, () => {
  console.log('\n🚀 飞行棋游戏开发服务器启动成功！');
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  console.log(`📍 服务器地址: http://${HOST}:${PORT}`);
  console.log(`📁 根目录: ${__dirname}`);
  console.log(`⏰ 启动时间: ${new Date().toLocaleString()}`);
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  console.log('\n💡 使用说明：');
  console.log('   • 在浏览器中打开上述地址开始游戏');
  console.log('   • 按 Ctrl+C 停止服务器');
  console.log('   • 修改代码后刷新浏览器即可看到更新');
  console.log('\n🎮 开始享受飞行棋游戏吧！\n');
});

// 处理命令行参数
const args = process.argv.slice(2);
for (let i = 0; i < args.length; i++) {
  if (args[i] === '--port' && args[i + 1]) {
    const customPort = parseInt(args[i + 1]);
    if (!isNaN(customPort) && customPort > 0 && customPort < 65536) {
      PORT = customPort;
    }
  }
}