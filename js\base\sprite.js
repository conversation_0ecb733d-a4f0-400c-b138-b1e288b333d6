import Emitter from '../libs/tinyemitter.js';

/**
 * 游戏基础的精灵类
 */
export default class Sprite extends Emitter {
  visible = true; // 是否可见
  isActive = true; // 是否可碰撞
  imgLoaded = false; // 图片是否加载成功
  imgError = false; // 图片是否加载失败
  defaultColor = '#CCCCCC'; // 默认颜色

  constructor(imgSrc = '', width = 0, height = 0, x = 0, y = 0) {
    super();
    
    this.width = width;
    this.height = height;
    this.x = x;
    this.y = y;
    this.visible = true;
    
    // 如果提供了有效的图片路径，尝试加载图片
    if (imgSrc && imgSrc.trim() !== '') {
      this.img = wx.createImage();
      
      // 设置加载成功回调
      this.img.onload = () => {
        this.imgLoaded = true;
        this.imgError = false;
      };
      
      // 设置加载失败回调
      this.img.onerror = (e) => {
        console.log('图片加载失败:', imgSrc, e);
        this.imgError = true;
      };
      
      // 设置图片路径
      this.img.src = imgSrc;
    } else {
      this.imgError = true;
    }
  }

  /**
   * 将精灵图绘制在canvas上
   */
  render(ctx) {
    if (!this.visible) return;

    // 如果图片加载成功，绘制图片
    if (this.imgLoaded && !this.imgError && this.img) {
      try {
        ctx.drawImage(this.img, this.x, this.y, this.width, this.height);
      } catch (e) {
        console.log('绘制图片失败:', e);
        this.drawFallback(ctx);
      }
    } else {
      // 否则绘制占位图形
      this.drawFallback(ctx);
    }
  }
  
  /**
   * 绘制备用图形
   */
  drawFallback(ctx) {
    // 保存上下文
    ctx.save();
    
    // 绘制矩形
    ctx.fillStyle = this.defaultColor;
    ctx.fillRect(this.x, this.y, this.width, this.height);
    
    // 绘制边框
    ctx.strokeStyle = '#999999';
    ctx.lineWidth = 1;
    ctx.strokeRect(this.x, this.y, this.width, this.height);
    
    // 绘制X标记
    ctx.beginPath();
    ctx.moveTo(this.x, this.y);
    ctx.lineTo(this.x + this.width, this.y + this.height);
    ctx.moveTo(this.x + this.width, this.y);
    ctx.lineTo(this.x, this.y + this.height);
    ctx.strokeStyle = '#999999';
    ctx.stroke();
    
    // 恢复上下文
    ctx.restore();
  }
  
  /**
   * 设置精灵颜色
   */
  setColor(color) {
    this.defaultColor = color;
  }

  /**
   * 简单的碰撞检测定义：
   * 另一个精灵的中心点处于本精灵所在的矩形内即可
   * @param{Sprite} sp: Sptite的实例
   */
  isCollideWith(sp) {
    const spX = sp.x + sp.width / 2;
    const spY = sp.y + sp.height / 2;

    // 不可见则不检测
    if (!this.visible || !sp.visible) return false;
    // 不可碰撞则不检测
    if (!this.isActive || !sp.isActive) return false;

    return !!(
      spX >= this.x &&
      spX <= this.x + this.width &&
      spY >= this.y &&
      spY <= this.y + this.height
    );
  }
}
