/**
 * 动画管理器
 * 负责管理游戏中的各种动画效果
 */
import { ANIMATION_CONFIG } from '../config/gameConfig';

class AnimationManager {
  constructor() {
    this.animations = new Map();
    this.animationId = 0;
    this.isRunning = false;
    
    // 缓动函数
    this.easingFunctions = {
      linear: t => t,
      easeInQuad: t => t * t,
      easeOutQuad: t => t * (2 - t),
      easeInOutQuad: t => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t,
      easeInCubic: t => t * t * t,
      easeOutCubic: t => (--t) * t * t + 1,
      easeInOutCubic: t => t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1
    };
  }
  
  /**
   * 创建动画
   * @param {Object} options - 动画选项
   * @returns {number} 动画ID
   */
  createAnimation(options) {
    const id = ++this.animationId;
    
    const animation = {
      id,
      target: options.target,
      from: { ...options.from },
      to: { ...options.to },
      duration: options.duration || 1000,
      easing: options.easing || 'easeInOutQuad',
      onUpdate: options.onUpdate || null,
      onComplete: options.onComplete || null,
      startTime: Date.now(),
      isCompleted: false
    };
    
    this.animations.set(id, animation);
    
    // 如果动画管理器未运行，启动它
    if (!this.isRunning) {
      this.start();
    }
    
    return id;
  }
  
  /**
   * 棋子移动动画
   * @param {Object} piece - 棋子对象
   * @param {number} targetX - 目标X坐标
   * @param {number} targetY - 目标Y坐标
   * @param {Function} onComplete - 完成回调
   */
  animatePieceMove(piece, targetX, targetY, onComplete = null) {
    const config = ANIMATION_CONFIG.pieceMove;
    
    return this.createAnimation({
      target: piece,
      from: { x: piece.x, y: piece.y },
      to: { x: targetX, y: targetY },
      duration: config.duration,
      easing: config.easing,
      onUpdate: (progress, values) => {
        piece.x = values.x;
        piece.y = values.y;
      },
      onComplete: () => {
        piece.isMoving = false;
        if (onComplete) onComplete();
      }
    });
  }
  
  /**
   * 棋子起飞动画
   * @param {Object} piece - 棋子对象
   * @param {number} targetX - 目标X坐标
   * @param {number} targetY - 目标Y坐标
   * @param {Function} onComplete - 完成回调
   */
  animatePieceTakeOff(piece, targetX, targetY, onComplete = null) {
    const config = ANIMATION_CONFIG.effects.takeOff;
    
    // 计算弧线路径
    const startX = piece.x;
    const startY = piece.y;
    const midX = (startX + targetX) / 2;
    const midY = Math.min(startY, targetY) - 50; // 弧线高度
    
    return this.createAnimation({
      target: piece,
      from: { t: 0 },
      to: { t: 1 },
      duration: config.duration,
      easing: 'easeInOutQuad',
      onUpdate: (progress, values) => {
        const t = values.t;
        // 贝塞尔曲线计算
        piece.x = Math.pow(1 - t, 2) * startX + 2 * (1 - t) * t * midX + Math.pow(t, 2) * targetX;
        piece.y = Math.pow(1 - t, 2) * startY + 2 * (1 - t) * t * midY + Math.pow(t, 2) * targetY;
        
        // 添加旋转效果
        piece.rotation = t * Math.PI * 2;
      },
      onComplete: () => {
        piece.isMoving = false;
        piece.rotation = 0;
        if (onComplete) onComplete();
      }
    });
  }
  
  /**
   * 棋子跳跃动画
   * @param {Object} piece - 棋子对象
   * @param {number} targetX - 目标X坐标
   * @param {number} targetY - 目标Y坐标
   * @param {Function} onComplete - 完成回调
   */
  animatePieceJump(piece, targetX, targetY, onComplete = null) {
    const startX = piece.x;
    const startY = piece.y;
    const jumpHeight = 30;
    
    return this.createAnimation({
      target: piece,
      from: { t: 0 },
      to: { t: 1 },
      duration: 400,
      easing: 'easeInOutQuad',
      onUpdate: (progress, values) => {
        const t = values.t;
        piece.x = startX + (targetX - startX) * t;
        piece.y = startY + (targetY - startY) * t - jumpHeight * Math.sin(t * Math.PI);
      },
      onComplete: () => {
        piece.isMoving = false;
        if (onComplete) onComplete();
      }
    });
  }
  
  /**
   * 棋子飞行动画
   * @param {Object} piece - 棋子对象
   * @param {number} targetX - 目标X坐标
   * @param {number} targetY - 目标Y坐标
   * @param {Function} onComplete - 完成回调
   */
  animatePieceFly(piece, targetX, targetY, onComplete = null) {
    return this.createAnimation({
      target: piece,
      from: { x: piece.x, y: piece.y, scale: 1 },
      to: { x: targetX, y: targetY, scale: 1 },
      duration: 200, // 飞行速度很快
      easing: 'easeInOutCubic',
      onUpdate: (progress, values) => {
        piece.x = values.x;
        piece.y = values.y;
        // 添加缩放效果
        piece.scale = 1 + 0.2 * Math.sin(progress * Math.PI);
      },
      onComplete: () => {
        piece.isMoving = false;
        piece.scale = 1;
        if (onComplete) onComplete();
      }
    });
  }
  
  /**
   * 棋子被击落动画
   * @param {Object} piece - 棋子对象
   * @param {number} targetX - 目标X坐标
   * @param {number} targetY - 目标Y坐标
   * @param {Function} onComplete - 完成回调
   */
  animatePieceHit(piece, targetX, targetY, onComplete = null) {
    const config = ANIMATION_CONFIG.effects.hit;
    
    return this.createAnimation({
      target: piece,
      from: { x: piece.x, y: piece.y, rotation: 0, scale: 1 },
      to: { x: targetX, y: targetY, rotation: Math.PI * 4, scale: 0.5 },
      duration: config.duration,
      easing: 'easeInCubic',
      onUpdate: (progress, values) => {
        piece.x = values.x;
        piece.y = values.y;
        piece.rotation = values.rotation;
        piece.scale = values.scale;
      },
      onComplete: () => {
        piece.isMoving = false;
        piece.rotation = 0;
        piece.scale = 1;
        if (onComplete) onComplete();
      }
    });
  }
  
  /**
   * 骰子投掷动画
   * @param {Object} dice - 骰子对象
   * @param {number} finalValue - 最终点数
   * @param {Function} onComplete - 完成回调
   */
  animateDiceRoll(dice, finalValue, onComplete = null) {
    const config = ANIMATION_CONFIG.diceRoll;
    
    return this.createAnimation({
      target: dice,
      from: { rotation: 0, scale: 1 },
      to: { rotation: Math.PI * 2 * config.rotations, scale: 1 },
      duration: config.duration,
      easing: 'easeOutCubic',
      onUpdate: (progress, values) => {
        dice.rotation = values.rotation;
        dice.scale = 1 + 0.1 * Math.sin(progress * Math.PI * 10);
        
        // 随机显示点数（投掷过程中）
        if (progress < 0.8) {
          dice.value = Math.floor(Math.random() * 6) + 1;
        } else {
          dice.value = finalValue;
        }
      },
      onComplete: () => {
        dice.isRolling = false;
        dice.rotation = 0;
        dice.scale = 1;
        dice.value = finalValue;
        if (onComplete) onComplete();
      }
    });
  }
  
  /**
   * 胜利庆祝动画
   * @param {Object} target - 目标对象
   * @param {Function} onComplete - 完成回调
   */
  animateWinCelebration(target, onComplete = null) {
    const config = ANIMATION_CONFIG.effects.win;
    
    return this.createAnimation({
      target,
      from: { scale: 1, rotation: 0 },
      to: { scale: 1, rotation: Math.PI * 4 },
      duration: config.duration,
      easing: 'easeInOutQuad',
      onUpdate: (progress, values) => {
        target.scale = 1 + 0.3 * Math.sin(progress * Math.PI * 6);
        target.rotation = values.rotation;
      },
      onComplete: () => {
        target.scale = 1;
        target.rotation = 0;
        if (onComplete) onComplete();
      }
    });
  }
  
  /**
   * 停止动画
   * @param {number} animationId - 动画ID
   */
  stopAnimation(animationId) {
    this.animations.delete(animationId);
  }
  
  /**
   * 停止所有动画
   */
  stopAllAnimations() {
    this.animations.clear();
  }
  
  /**
   * 启动动画循环
   */
  start() {
    if (this.isRunning) return;
    
    this.isRunning = true;
    this.update();
  }
  
  /**
   * 停止动画循环
   */
  stop() {
    this.isRunning = false;
  }
  
  /**
   * 更新动画
   */
  update() {
    if (!this.isRunning) return;
    
    const currentTime = Date.now();
    const completedAnimations = [];
    
    this.animations.forEach((animation, id) => {
      if (animation.isCompleted) {
        completedAnimations.push(id);
        return;
      }
      
      const elapsed = currentTime - animation.startTime;
      const progress = Math.min(elapsed / animation.duration, 1);
      
      // 应用缓动函数
      const easingFunc = this.easingFunctions[animation.easing] || this.easingFunctions.linear;
      const easedProgress = easingFunc(progress);
      
      // 计算当前值
      const currentValues = {};
      for (const key in animation.from) {
        const from = animation.from[key];
        const to = animation.to[key];
        currentValues[key] = from + (to - from) * easedProgress;
      }
      
      // 调用更新回调
      if (animation.onUpdate) {
        animation.onUpdate(easedProgress, currentValues);
      }
      
      // 检查动画是否完成
      if (progress >= 1) {
        animation.isCompleted = true;
        if (animation.onComplete) {
          animation.onComplete();
        }
        completedAnimations.push(id);
      }
    });
    
    // 移除已完成的动画
    completedAnimations.forEach(id => {
      this.animations.delete(id);
    });
    
    // 如果还有动画在运行，继续更新
    if (this.animations.size > 0) {
      requestAnimationFrame(() => this.update());
    } else {
      this.isRunning = false;
    }
  }
}

// 创建单例实例
const animationManager = new AnimationManager();

export default animationManager;