<svg width="750" height="1334" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 渐变定义 -->
    <radialGradient id="bgGradient" cx="50%" cy="50%" r="70%">
      <stop offset="0%" stop-color="#E8F5E8"/>
      <stop offset="100%" stop-color="#C8E6C9"/>
    </radialGradient>
    
    <linearGradient id="boardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#FFFFFF"/>
      <stop offset="100%" stop-color="#F8F9FA"/>
    </linearGradient>
    
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="8" stdDeviation="12" flood-color="rgba(0,0,0,0.15)"/>
    </filter>
    
    <filter id="innerShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset dx="0" dy="2"/>
      <feGaussianBlur stdDeviation="3" result="offset-blur"/>
      <feFlood flood-color="rgba(0,0,0,0.1)"/>
      <feComposite in2="offset-blur" operator="in"/>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="750" height="1334" fill="url(#bgGradient)"/>
  
  <!-- 装饰性圆圈 -->
  <circle cx="150" cy="150" r="80" fill="rgba(76, 175, 80, 0.1)" opacity="0.6"/>
  <circle cx="600" cy="1200" r="100" fill="rgba(33, 150, 243, 0.1)" opacity="0.6"/>
  <circle cx="50" cy="1000" r="60" fill="rgba(255, 193, 7, 0.1)" opacity="0.6"/>
  
  <!-- 棋盘主体 -->
  <rect x="50" y="250" width="650" height="650" fill="url(#boardGradient)" stroke="#2E7D32" stroke-width="6" rx="25" filter="url(#shadow)"/>
  
  <!-- 棋盘内部装饰边框 -->
  <rect x="60" y="260" width="630" height="630" fill="none" stroke="rgba(46, 125, 50, 0.3)" stroke-width="2" rx="20"/>
  
  <!-- 中心十字区域 -->
  <rect x="300" y="250" width="150" height="650" fill="rgba(255, 255, 255, 0.8)" stroke="#2E7D32" stroke-width="3" filter="url(#innerShadow)"/>
  <rect x="50" y="500" width="650" height="150" fill="rgba(255, 255, 255, 0.8)" stroke="#2E7D32" stroke-width="3" filter="url(#innerShadow)"/>
  
  <!-- 四个基地区域 -->
  <!-- 红色基地 (左上) -->
  <rect x="75" y="275" width="200" height="200" fill="#FFEBEE" stroke="#F44336" stroke-width="4" rx="15" filter="url(#shadow)"/>
  <circle cx="175" cy="375" r="35" fill="#F44336" opacity="0.2"/>
  <circle cx="175" cy="375" r="25" fill="#F44336" opacity="0.4"/>
  <text x="175" y="385" text-anchor="middle" font-family="'Microsoft YaHei', Arial, sans-serif" font-size="20" font-weight="bold" fill="#F44336">红方</text>
  
  <!-- 黄色基地 (右上) -->
  <rect x="475" y="275" width="200" height="200" fill="#FFFDE7" stroke="#FFEB3B" stroke-width="4" rx="15" filter="url(#shadow)"/>
  <circle cx="575" cy="375" r="35" fill="#FFEB3B" opacity="0.2"/>
  <circle cx="575" cy="375" r="25" fill="#FFEB3B" opacity="0.4"/>
  <text x="575" y="385" text-anchor="middle" font-family="'Microsoft YaHei', Arial, sans-serif" font-size="20" font-weight="bold" fill="#F57F17">黄方</text>
  
  <!-- 蓝色基地 (右下) -->
  <rect x="475" y="675" width="200" height="200" fill="#E3F2FD" stroke="#2196F3" stroke-width="4" rx="15" filter="url(#shadow)"/>
  <circle cx="575" cy="775" r="35" fill="#2196F3" opacity="0.2"/>
  <circle cx="575" cy="775" r="25" fill="#2196F3" opacity="0.4"/>
  <text x="575" y="785" text-anchor="middle" font-family="'Microsoft YaHei', Arial, sans-serif" font-size="20" font-weight="bold" fill="#2196F3">蓝方</text>
  
  <!-- 绿色基地 (左下) -->
  <rect x="75" y="675" width="200" height="200" fill="#E8F5E8" stroke="#4CAF50" stroke-width="4" rx="15" filter="url(#shadow)"/>
  <circle cx="175" cy="775" r="35" fill="#4CAF50" opacity="0.2"/>
  <circle cx="175" cy="775" r="25" fill="#4CAF50" opacity="0.4"/>
  <text x="175" y="785" text-anchor="middle" font-family="'Microsoft YaHei', Arial, sans-serif" font-size="20" font-weight="bold" fill="#4CAF50">绿方</text>
  
  <!-- 中心终点区域 -->
  <circle cx="375" cy="575" r="70" fill="#FFF3E0" stroke="#FF9800" stroke-width="4" filter="url(#shadow)"/>
  <circle cx="375" cy="575" r="50" fill="#FF9800" opacity="0.3"/>
  <circle cx="375" cy="575" r="30" fill="#FF9800" opacity="0.6"/>
  <text x="375" y="585" text-anchor="middle" font-family="'Microsoft YaHei', Arial, sans-serif" font-size="18" font-weight="bold" fill="#FF9800">终点</text>
  
  <!-- 游戏标题 -->
  <text x="375" y="150" text-anchor="middle" font-family="'Microsoft YaHei', Arial, sans-serif" font-size="52" font-weight="bold" fill="#2E7D32" filter="url(#shadow)">飞行棋</text>
  <text x="375" y="200" text-anchor="middle" font-family="'Microsoft YaHei', Arial, sans-serif" font-size="18" fill="#66BB6A">经典策略棋类游戏</text>
  
  <!-- 底部信息区域 -->
  <rect x="50" y="950" width="650" height="120" fill="rgba(255, 255, 255, 0.9)" stroke="#E0E0E0" stroke-width="3" rx="15" filter="url(#shadow)"/>
  <text x="375" y="990" text-anchor="middle" font-family="'Microsoft YaHei', Arial, sans-serif" font-size="20" font-weight="bold" fill="#424242">游戏规则</text>
  <text x="375" y="1015" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#757575">投掷骰子 • 移动棋子 • 率先到达终点获胜</text>
  <text x="375" y="1035" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#757575">投出6点可起飞或再投一次</text>
  
  <!-- 装饰性星星 -->
  <polygon points="100,50 105,65 120,65 108,75 113,90 100,80 87,90 92,75 80,65 95,65" fill="#FFD700" opacity="0.7"/>
  <polygon points="650,100 655,115 670,115 658,125 663,140 650,130 637,140 642,125 630,115 645,115" fill="#FFD700" opacity="0.7"/>
  <polygon points="50,1250 55,1265 70,1265 58,1275 63,1290 50,1280 37,1290 42,1275 30,1265 45,1265" fill="#FFD700" opacity="0.7"/>
</svg>